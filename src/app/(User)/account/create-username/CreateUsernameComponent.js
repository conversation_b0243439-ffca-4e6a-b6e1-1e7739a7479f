'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect, useCallback } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import StatusIndicator from "@/Components/UI/StatusIndicator";
import { getUsernameInfo, checkUsernameAvailability, updateUsername } from "@/utils/auth";
import { useRouter, useSearchParams } from "next/navigation";
import { useSelector } from 'react-redux';
import { useSecurityCookieMonitor } from "@/Hooks/useSecurityCookieMonitor";
import { Formik, Field, Form } from "formik";
import { accountUsernameSchema } from "@/validations/schema";
import InputError from "@/Components/UI/InputError";
import { CheckIcon } from "@/assets/svgIcons/SvgIcon";
import debounce from "lodash.debounce";
import "@/css/account/AccountDetails.scss";

export default function CreateUsernameComponent() {
    const [usernameInfo, setUsernameInfo] = useState(null);
    const [isLoadingUserData, setIsLoadingUserData] = useState(true);
    const [saveStatus, setSaveStatus] = useState(null); // 'loading', 'success', 'error', null
    const [error, setError] = useState(null);
    const [availabilityStatus, setAvailabilityStatus] = useState(null); // 'checking', 'available', 'taken', 'current'
    const [availabilityMessage, setAvailabilityMessage] = useState('');
    const [suggestions, setSuggestions] = useState([]);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const router = useRouter();
    const searchParams = useSearchParams();
    const reduxUser = useSelector((state) => state?.auth?.user || null);

    // Security cookie monitoring
    useSecurityCookieMonitor();

    const metaArray = {
        noindex: true,
        title: "Create Username | TradeReply",
        description: "Create or update your username on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/create-username",
        og_site_name: "TradeReply",
        og_title: "Create Username | TradeReply",
        og_description: "Create or update your username on TradeReply.com.",
        twitter_title: "Create Username | TradeReply",
        twitter_description: "Create or update your username on TradeReply.com.",
    };

    // Fetch user username information on component mount
    useEffect(() => {
        const fetchUsernameInfo = async () => {
            try {
                setIsLoadingUserData(true);
                const response = await getUsernameInfo();
                
                if (response.success) {
                    setUsernameInfo(response.data);
                } else {
                    setError(response.message || 'Failed to load username information');
                }
            } catch (err) {
                setError('Failed to load username information');
            } finally {
                setIsLoadingUserData(false);
            }
        };

        fetchUsernameInfo();
    }, []);

    // Debounced username availability check
    const checkAvailability = useCallback(
        debounce(async (username) => {
            if (!username || username.length < 3) {
                setAvailabilityStatus(null);
                setAvailabilityMessage('');
                setSuggestions([]);
                return;
            }

            setAvailabilityStatus('checking');
            setAvailabilityMessage('Checking availability...');

            try {
                const response = await checkUsernameAvailability(username);
                
                if (response.success) {
                    if (response.data.is_current) {
                        setAvailabilityStatus('current');
                        setAvailabilityMessage('This is your current username');
                    } else {
                        setAvailabilityStatus('available');
                        setAvailabilityMessage('Username is available');
                    }
                    setSuggestions([]);
                } else {
                    setAvailabilityStatus('taken');
                    setAvailabilityMessage(response.message);
                    setSuggestions(response.suggestions || []);
                }
            } catch (err) {
                setAvailabilityStatus('error');
                setAvailabilityMessage('Error checking availability');
                setSuggestions([]);
            }
        }, 500),
        []
    );

    // Handle form submission
    const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
        try {
            setIsSubmitting(true);
            setSaveStatus('loading');
            setError(null);

            const response = await updateUsername(values.username);

            if (response.success) {
                setSaveStatus('success');
                setUsernameInfo(response.data);
                
                // Handle redirect based on referrer
                const from = searchParams.get('from');
                if (from) {
                    router.push(decodeURIComponent(from));
                } else {
                    // Default redirect to account details
                    setTimeout(() => {
                        router.push('/account/details');
                    }, 2000);
                }
            } else {
                setSaveStatus('error');
                setError(response.message);
                if (response.message.includes('already taken')) {
                    setFieldError('username', response.message);
                }
            }
        } catch (err) {
            setSaveStatus('error');
            setError('An unexpected error occurred');
        } finally {
            setIsSubmitting(false);
            setSubmitting(false);
        }
    };

    // Handle suggestion click
    const handleSuggestionClick = (suggestion, setFieldValue) => {
        setFieldValue('username', suggestion);
        checkAvailability(suggestion);
    };

    // Show loading state while fetching user data
    if (isLoadingUserData) {
        return (
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_create_username">
                    <SidebarHeading title="Loading..." />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <div className="account_header_main">
                                        <h6>Username</h6>
                                    </div>
                                    <p>Loading your username information...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </AccountLayout>
        );
    }

    const initialValues = {
        username: usernameInfo?.username || ''
    };

    const isUpdating = !!usernameInfo?.username;
    const canChange = usernameInfo?.can_change ?? true;

    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_create_username">
                    <SidebarHeading title={isUpdating ? "Update Username" : "Create Username"} />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <div className="account_header_main justify-content-start">
                                        <div>
                                            <h6>Username</h6>
                                        </div>
                                        <div className="account_status_indicator">
                                            <StatusIndicator
                                                saveStatus={saveStatus}
                                                error={error}
                                                successText="Username updated"
                                                defaultText="Not saved"
                                            />
                                        </div>
                                    </div>
                                    <p>
                                        {isUpdating 
                                            ? `Choose a new username for your TradeReply account. You have ${usernameInfo?.changes_remaining || 0} changes remaining.`
                                            : "Choose a unique username for your TradeReply account. This will be your public identifier and can be changed up to 2 times."
                                        }
                                    </p>
                                    {!canChange && (
                                        <div className="alert alert-warning mt-2">
                                            <strong>Username change limit reached.</strong> You have used all 2 allowed username changes.
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <Formik
                                    initialValues={initialValues}
                                    validationSchema={accountUsernameSchema}
                                    onSubmit={handleSubmit}
                                    enableReinitialize={true}
                                >
                                    {({ values, errors, touched, setFieldValue, isSubmitting: formikSubmitting }) => (
                                        <Form>
                                            <div className="account_card_list">
                                                <div className="col-lg-5 col-md-8 col-12">
                                                    <div className="authCorrectIcon">
                                                        <div className="checkIcon">
                                                            {values.username && 
                                                             availabilityStatus === 'available' && 
                                                             values.username.length >= 3 && 
                                                             <CheckIcon />}
                                                        </div>
                                                        <Field name="username">
                                                            {({ field }) => (
                                                                <TextInput
                                                                    {...field}
                                                                    type="text"
                                                                    placeholder="Enter your username"
                                                                    maxLength={20}
                                                                    disabled={!canChange || isSubmitting}
                                                                    onChange={(e) => {
                                                                        field.onChange(e);
                                                                        checkAvailability(e.target.value);
                                                                    }}
                                                                    isError={
                                                                        (touched.username && errors.username) ||
                                                                        availabilityStatus === 'taken'
                                                                    }
                                                                />
                                                            )}
                                                        </Field>
                                                    </div>
                                                    
                                                    {/* Validation errors */}
                                                    {touched.username && errors.username && (
                                                        <InputError message={errors.username} />
                                                    )}
                                                    
                                                    {/* Availability status */}
                                                    {availabilityMessage && (
                                                        <div className={`availability-message mt-2 ${
                                                            availabilityStatus === 'available' ? 'text-success' :
                                                            availabilityStatus === 'taken' ? 'text-danger' :
                                                            availabilityStatus === 'current' ? 'text-info' :
                                                            'text-muted'
                                                        }`}>
                                                            {availabilityMessage}
                                                        </div>
                                                    )}
                                                    
                                                    {/* Username suggestions */}
                                                    {suggestions.length > 0 && (
                                                        <div className="username-suggestions mt-3">
                                                            <p className="mb-2"><strong>Suggestions:</strong></p>
                                                            <div className="suggestions-list">
                                                                {suggestions.map((suggestion, index) => (
                                                                    <button
                                                                        key={index}
                                                                        type="button"
                                                                        className="btn btn-outline-secondary btn-sm me-2 mb-2"
                                                                        onClick={() => handleSuggestionClick(suggestion, setFieldValue)}
                                                                        disabled={isSubmitting}
                                                                    >
                                                                        {suggestion}
                                                                    </button>
                                                                ))}
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                                
                                                <div className="col-12 mt-3">
                                                    <div className="username-rules">
                                                        <h6>Username Requirements:</h6>
                                                        <ul>
                                                            <li>Must be between 3-20 characters</li>
                                                            <li>Must start with a letter</li>
                                                            <li>Can contain letters, numbers, and underscores</li>
                                                            <li>Cannot contain consecutive underscores</li>
                                                            <li>Cannot contain spaces or special characters</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div className="account_card_list_btns mt-3">
                                                <button 
                                                    type="button"
                                                    className="btn-style white-btn"
                                                    onClick={() => {
                                                        const from = searchParams.get('from');
                                                        if (from) {
                                                            router.push(decodeURIComponent(from));
                                                        } else {
                                                            router.push('/account/details');
                                                        }
                                                    }}
                                                    disabled={isSubmitting}
                                                >
                                                    Cancel
                                                </button>
                                                <button 
                                                    type="submit"
                                                    className="btn-style"
                                                    disabled={
                                                        !canChange || 
                                                        isSubmitting || 
                                                        formikSubmitting ||
                                                        !values.username ||
                                                        availabilityStatus === 'taken' ||
                                                        availabilityStatus === 'current' ||
                                                        (touched.username && errors.username)
                                                    }
                                                >
                                                    {isSubmitting ? 'Saving...' : (isUpdating ? 'Update Username' : 'Save Username')}
                                                </button>
                                            </div>
                                        </Form>
                                    )}
                                </Formik>
                            </div>
                        </div>
                    </div>
                </div>
            </AccountLayout>
        </>
    )
}
