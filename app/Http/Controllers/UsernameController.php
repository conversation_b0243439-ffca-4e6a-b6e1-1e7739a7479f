<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Rules\NoProfanity;
use App\Rules\ValidUsername;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class UsernameController extends Controller
{
    use ApiResponseTrait;

    /**
     * Get current username information
     */
    public function show()
    {
        $user = auth()->user();
        
        return $this->successResponse([
            'username' => $user->username,
            'username_change_count' => $user->username_change_count ?? 0,
            'changes_remaining' => max(0, 2 - ($user->username_change_count ?? 0)),
            'can_change' => ($user->username_change_count ?? 0) < 2
        ], 'Username information retrieved successfully');
    }

    /**
     * Check username availability
     */
    public function checkAvailability(Request $request)
    {
        $request->validate([
            'username' => [
                'required',
                'string',
                'min:3',
                'max:20',
                new ValidUsername(),
                new NoProfanity(),
            ]
        ]);

        $username = strtolower($request->username);
        $user = auth()->user();

        // Check if it's the user's current username
        if ($user->username && strtolower($user->username) === $username) {
            return $this->successResponse([
                'available' => true,
                'is_current' => true,
                'message' => 'This is your current username'
            ], 'Username check completed');
        }

        // Check if username is taken by another user
        $exists = User::whereRaw('LOWER(username) = ?', [$username])
            ->where('id', '!=', $user->id)
            ->exists();

        if ($exists) {
            $suggestions = $this->generateUsernameSuggestions($username);
            return $this->errorResponse(
                'This username is already taken.',
                400,
                ['suggestions' => $suggestions]
            );
        }

        return $this->successResponse([
            'available' => true,
            'is_current' => false,
            'message' => 'Username is available'
        ], 'Username is available');
    }

    /**
     * Update username for authenticated user
     */
    public function update(Request $request)
    {
        $user = auth()->user();

        // Check if user has reached change limit
        if (($user->username_change_count ?? 0) >= 2) {
            return $this->errorResponse(
                'You have reached the maximum number of username changes (2).',
                422
            );
        }

        $request->validate([
            'username' => [
                'required',
                'string',
                'min:3',
                'max:20',
                new ValidUsername(),
                new NoProfanity(),
                Rule::unique('users', 'username')->ignore($user->id),
            ]
        ]);

        $newUsername = strtolower($request->username);

        // Check if it's the same as current username
        if ($user->username && strtolower($user->username) === $newUsername) {
            return $this->errorResponse(
                'This is already your current username.',
                422
            );
        }

        DB::transaction(function () use ($user, $newUsername) {
            // Update username and increment change count
            $user->update([
                'username' => $newUsername,
                'username_change_count' => ($user->username_change_count ?? 0) + 1
            ]);
        });

        $changesRemaining = max(0, 2 - $user->username_change_count);

        return $this->successResponse([
            'username' => $user->username,
            'username_change_count' => $user->username_change_count,
            'changes_remaining' => $changesRemaining,
            'can_change' => $changesRemaining > 0
        ], 'Username updated successfully');
    }

    /**
     * Get username change history (placeholder for future implementation)
     */
    public function getChangeHistory()
    {
        $user = auth()->user();
        
        return $this->successResponse([
            'current_username' => $user->username,
            'change_count' => $user->username_change_count ?? 0,
            'changes_remaining' => max(0, 2 - ($user->username_change_count ?? 0)),
            // Future: Add actual change history from a separate table
            'history' => []
        ], 'Username change history retrieved');
    }

    /**
     * Generate username suggestions
     */
    private function generateUsernameSuggestions($base, $count = 4)
    {
        $suggestions = [];
        $base = substr($base, 0, 15);

        for ($i = 0; $i < $count * 2; $i++) {
            $candidate = $base . rand(1, 9999);

            if (!User::whereRaw('LOWER(username) = ?', [strtolower($candidate)])->exists()) {
                $suggestions[] = $candidate;
            }

            if (count($suggestions) >= $count) {
                break;
            }
        }

        return $suggestions;
    }
}
